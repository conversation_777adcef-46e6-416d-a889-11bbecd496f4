<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class VehicleUnit extends Model
{
    protected $fillable = [
        'driver_id',
        'placa',
        'modelo',
        'marca',
        'ano',
        'color',
        'chasis',
        'motor',
        'tipo',
        'estado',
        'imagen',
        'file',
        'observaciones',
    ];

    public function driver(): BelongsTo
    {
        return $this->belongsTo(
            related: Driver::class,
            foreignKey: 'driver_id',
        );
    }
}
