<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Driver extends Model
{
    /** @use HasFactory<\Database\Factories\DriverFactory> */
    use HasFactory;

    protected $fillable = [
        'name',
        'last_paternal_name',
        'last_maternal_name',
        'dni',
        'cargo_id',
    ];

    /**
     * Get the cargo that the driver belongs to.
     */
    public function cargo(): BelongsTo
    {
        return $this->belongsTo(
            related: Cargo::class,
            foreignKey: 'cargo_id',
        );
    }

    public function licenses(): HasMany
    {
        return $this->hasMany(
            related: DriversLicense::class,
            foreignKey: 'driver_id',
        );
    }

    public function vehicleUnits(): HasMany
    {
        return $this->hasMany(
            related: VehicleUnit::class,
            foreignKey: 'driver_id',
        );
    }
}
