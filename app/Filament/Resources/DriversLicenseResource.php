<?php

namespace App\Filament\Resources;

use App\Filament\Resources\DriversLicenseResource\Pages;
use App\Models\Driver;
use App\Models\DriversLicense;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;

class DriversLicenseResource extends Resource
{
    protected static ?string $model = DriversLicense::class;

    protected static ?string $navigationIcon = 'heroicon-o-identification';

    protected static ?string $modelLabel = 'Licencia';

    public static function form(Form $form): Form
    {
        // /AIzaSyC1uVDwK1TWt0A6jXcJkAan4xbGprM1MfM
        return $form
            ->schema([
                Forms\Components\Select::make('driver_id')
                    ->label('Chofer')
                    ->searchable()
                    ->getSearchResultsUsing(fn (string $search): array => Driver::where('dni', 'like', "%{$search}%")->limit(50)->get()->mapWithKeys(function ($driver) {
                        return [$driver->id => "{$driver->name} {$driver->last_paternal_name} {$driver->last_maternal_name}"];
                    })->toArray())
                    ->getOptionLabelsUsing(fn (array $values): array => Driver::whereIn('id', $values)->get()->mapWithKeys(function ($driver) {
                        return [$driver->id => "{$driver->name} {$driver->last_paternal_name} {$driver->last_maternal_name}"];
                    })->toArray())
                    ->required(),
                Forms\Components\TextInput::make('license_number')
                    ->required()
                    ->maxLength(255),
                Forms\Components\DatePicker::make('expiration_date')
                    ->required(),
                Forms\Components\TextInput::make('license_type')
                    ->required()
                    ->maxLength(255),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('driver.name')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('license_number')
                    ->searchable(),
                Tables\Columns\TextColumn::make('expiration_date')
                    ->date()
                    ->sortable()
                    ->color(function ($record): string {
                        $expirationDate = \Carbon\Carbon::parse($record->expiration_date);
                        $today = now();
                        $daysUntilExpiration = $today->diffInDays($expirationDate, false);

                        // Si ya expiró (fecha pasada)
                        if ($daysUntilExpiration < 0) {
                            return 'danger';
                        }

                        // Si expira en los próximos 30 días
                        if ($daysUntilExpiration <= 30) {
                            return 'warning';
                        }

                        // Si está vigente por más de 30 días
                        return 'success';
                    })
                    ->icon(function ($record): string {
                        $expirationDate = \Carbon\Carbon::parse($record->expiration_date);
                        $today = now();
                        $daysUntilExpiration = $today->diffInDays($expirationDate, false);

                        // Si ya expiró (fecha pasada)
                        if ($daysUntilExpiration < 0) {
                            return 'heroicon-o-x-circle';
                        }

                        // Si expira en los próximos 30 días
                        if ($daysUntilExpiration <= 30) {
                            return 'heroicon-o-exclamation-circle';
                        }

                        // Si está vigente por más de 30 días
                        return 'heroicon-o-check-circle';
                    }),
                Tables\Columns\TextColumn::make('license_type')
                    ->searchable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListDriversLicenses::route('/'),
            'create' => Pages\CreateDriversLicense::route('/create'),
            'view' => Pages\ViewDriversLicense::route('/{record}'),
            'edit' => Pages\EditDriversLicense::route('/{record}/edit'),
        ];
    }
}
