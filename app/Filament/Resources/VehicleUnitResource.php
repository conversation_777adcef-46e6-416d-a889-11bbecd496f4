<?php

namespace App\Filament\Resources;

use App\Filament\Resources\VehicleUnitResource\Pages;
use App\Models\Driver;
use App\Models\VehicleUnit;
use Asmit\FilamentUpload\Forms\Components\AdvancedFileUpload;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Wallo\FilamentSelectify\Components\ToggleButton;

class VehicleUnitResource extends Resource
{
    protected static ?string $model = VehicleUnit::class;

    protected static ?string $navigationIcon = 'eos-drive-eta-o';

    protected static ?string $modelLabel = 'Vehiculos';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Veiculo')
                    ->description('Datos del Vehiculo')
                    ->schema([
                        Forms\Components\FileUpload::make('imagen')
                            ->default(null)
                            ->imageEditor()
                            ->disk('public')
                            ->directory('vehiculos')
                            ->imageResizeMode('contain')
                            ->imageCropAspectRatio('3:2'),
                        Forms\Components\Grid::make()
                            ->columns(2)
                            ->schema([
                                Forms\Components\Select::make('driver_id')
                                    ->label('Chofer')
                                    ->searchable()
                                    ->getSearchResultsUsing(fn (string $search): array => Driver::where('dni', 'like', "%{$search}%")->limit(50)->get()->mapWithKeys(function ($driver) {
                                        return [$driver->id => "{$driver->name} {$driver->last_paternal_name} {$driver->last_maternal_name}"];
                                    })->toArray())
                                    ->getOptionLabelsUsing(fn (array $values): array => Driver::whereIn('id', $values)->get()->mapWithKeys(function ($driver) {
                                        return [$driver->id => "{$driver->name} {$driver->last_paternal_name} {$driver->last_maternal_name}"];
                                    })->toArray())
                                    ->required(),
                                Forms\Components\TextInput::make('placa')
                                    ->required()
                                    ->maxLength(255),
                                Forms\Components\TextInput::make('modelo')
                                    ->required()
                                    ->maxLength(255),
                                Forms\Components\TextInput::make('marca')
                                    ->required()
                                    ->maxLength(255),
                                Forms\Components\TextInput::make('ano')
                                    ->required()
                                    ->maxLength(255),
                                Forms\Components\TextInput::make('color')
                                    ->required()
                                    ->maxLength(255),
                                Forms\Components\TextInput::make('chasis')
                                    ->required()
                                    ->maxLength(255),
                                Forms\Components\TextInput::make('motor')
                                    ->required()
                                    ->maxLength(255),
                                Forms\Components\TextInput::make('tipo')
                                    ->required()
                                    ->maxLength(255),
                                ToggleButton::make('estado')
                                    ->offColor('danger')
                                    ->onColor('primary')
                                    ->offLabel('No')
                                    ->onLabel('Yes')
                                    ->default(true),
                            ]),
                        Forms\Components\RichEditor::make('observaciones')
                            ->label('Observaciones')
                            ->columnSpanFull(),
                        AdvancedFileUpload::make('file')
                            ->label('Archivo')
                            ->multiple()
                            ->pdfPreviewHeight(400)
                            ->pdfDisplayPage(1)
                            ->pdfToolbar(true)
                            ->pdfZoomLevel(100)
                            ->required(),

                    ]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('driver.name')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('placa')
                    ->searchable(),
                Tables\Columns\TextColumn::make('modelo')
                    ->searchable(),
                Tables\Columns\TextColumn::make('marca')
                    ->searchable(),
                Tables\Columns\TextColumn::make('ano')
                    ->searchable(),
                Tables\Columns\TextColumn::make('color')
                    ->searchable(),
                Tables\Columns\TextColumn::make('chasis')
                    ->searchable(),
                Tables\Columns\TextColumn::make('motor')
                    ->searchable(),
                Tables\Columns\TextColumn::make('tipo')
                    ->searchable(),
                Tables\Columns\IconColumn::make('estado')
                    ->boolean(),
                Tables\Columns\TextColumn::make('imagen')
                    ->searchable(),
                Tables\Columns\TextColumn::make('file')
                    ->searchable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListVehicleUnits::route('/'),
            'create' => Pages\CreateVehicleUnit::route('/create'),
            'edit' => Pages\EditVehicleUnit::route('/{record}/edit'),
        ];
    }
}
