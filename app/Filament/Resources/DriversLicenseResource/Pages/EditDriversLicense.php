<?php

namespace App\Filament\Resources\DriversLicenseResource\Pages;

use App\Filament\Resources\DriversLicenseResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditDriversLicense extends EditRecord
{
    protected static string $resource = DriversLicenseResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\ViewAction::make(),
            Actions\DeleteAction::make(),
        ];
    }
}
