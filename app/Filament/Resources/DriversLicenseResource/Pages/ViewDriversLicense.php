<?php

namespace App\Filament\Resources\DriversLicenseResource\Pages;

use App\Filament\Resources\DriversLicenseResource;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;

class ViewDriversLicense extends ViewRecord
{
    protected static string $resource = DriversLicenseResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\EditAction::make(),
        ];
    }
}
