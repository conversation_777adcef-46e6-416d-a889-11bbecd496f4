<?php

namespace App\Filament\Resources\DriverResource\Pages;

use Filament\Infolists;
use Filament\Infolists\Infolist;
use Filament\Resources\Pages\ViewRecord;
use App\Filament\Resources\DriverResource;
use Asmit\FilamentUpload\Forms\Components\AdvancedFileUpload;
use Joaopaulolndev\FilamentPdfViewer\Forms\Components\PdfViewerField;

class ViewDriver extends ViewRecord
{
    protected static string $resource = DriverResource::class;

    public function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->schema([
                Infolists\Components\Section::make('Datos del Chofer')
                    ->description('Datos generales del chofer y documnetos')
                    ->icon('heroicon-o-cog')
                    ->collapsible()
                    ->schema([
                        Infolists\Components\Grid::make()
                            ->columns(4)
                            ->schema([
                                Infolists\Components\Card::make('Datos Personales')
                                    ->columnSpan(1)
                                    ->schema([
                                        Infolists\Components\ViewEntry::make('name')
                                            ->label('Nombre'),
                                        Infolists\Components\ViewEntry::make('last_paternal_name')
                                            ->label('Apellido Paterno'),
                                        Infolists\Components\ViewEntry::make('last_maternal_name')
                                            ->label('Apellido Materno'),
                                        Infolists\Components\ViewEntry::make('dni')
                                            ->label('DNI'),
                                        Infolists\Components\ViewEntry::make('cargo_id')
                                            ->label('Cargo'),
                                    ]),

                                Infolists\Components\Card::make()
                                    ->columnSpan(3)
                                    ->schema([
                                        Infolists\Components\Grid::make()
                                            ->schema([
                                                PdfViewerField::make('file')
                                                    ->label('View the PDF')
                                                    ->minHeight('40svh')
                                            ]),

                                    ]),
                            ]),
                    ]),
            ]);
    }
}
