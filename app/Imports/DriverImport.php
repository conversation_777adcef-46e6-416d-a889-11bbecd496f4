<?php

namespace App\Imports;

use App\Models\Cargo;
use App\Models\Driver;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Log;
use Maatwebsite\Excel\Concerns\ToCollection;
use Maatwebsite\Excel\Concerns\WithHeadingRow;

class DriverImport implements ToCollection, WithHeadingRow
{
    /**
     * @param  Collection  $collection
     */
    public function collection(Collection $rows)
    {
        // Debug: Log total rows received
        Log::info('DriverImport: Starting import with '.$rows->count().' rows');

        foreach ($rows as $index => $row) {
            try {
                // Debug: Log each row being processed
                Log::info('Processing row '.($index + 1), $row->toArray());

                // Check if required fields exist
                if (empty($row['DNI']) || empty($row['NOMBRES']) || empty($row['CARGO'])) {
                    Log::warning('Skipping row '.($index + 1).' - missing required fields', $row->toArray());

                    continue;
                }

                // Skip if DNI already exists (based on user preference)
                if (Driver::where('dni', $row['DNI'])->exists()) {
                    Log::info("Skipping driver with DNI {$row['DNI']} - already exists");

                    continue;
                }

                // Find cargo by name, skip if not found
                $cargo = Cargo::where('name', $row['CARGO'])->first();
                if (! $cargo) {
                    Log::warning("Cargo '{$row['CARGO']}' not found for driver {$row['NOMBRES']} {$row['APELLIDO PATERNO']}");

                    continue;
                }

                $driverData = [
                    'last_paternal_name' => $row['APELLIDO PATERNO'] ?? '',
                    'last_maternal_name' => $row['APELLIDO MATERNO'] ?? '',
                    'name' => $row['NOMBRES'],
                    'dni' => $row['DNI'],
                    'cargo_id' => $cargo->id,
                ];

                // Debug: Log data before creating
                Log::info('Creating driver with data:', $driverData);

                $driver = Driver::create($driverData);

                // Debug: Log successful creation
                Log::info('Successfully created driver with ID: '.$driver->id);
            } catch (\Exception $e) {
                Log::error('Error importing driver row '.($index + 1).': '.$e->getMessage(), [
                    'row' => $row->toArray(),
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString(),
                ]);

                continue;
            }
        }

        Log::info('DriverImport: Import process completed');
    }
}
