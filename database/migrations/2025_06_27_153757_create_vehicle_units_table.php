<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('vehicle_units', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('driver_id');
            $table->string('placa');
            $table->string('modelo');
            $table->string('marca');
            $table->string('ano');
            $table->string('color');
            $table->string('chasis');
            $table->string('motor');
            $table->string('tipo');
            $table->boolean('estado')->default(true);
            $table->string('imagen')->nullable();
            $table->string('file');
            $table->text('observaciones')->nullable();
            $table->foreign('driver_id')->references('id')->on('drivers')->cascadeOnDelete();

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('vehicle_units');
    }
};
